import Foundation
import ServiceManagement

class LoginItemManager {
    /// Logger for login item operations
    private static let logger = LoggingService.shared
    private static let serviceName = "LoginItemManager"

    static func setStartAtLogin(_ enable: Bool) {
        logger.info("Setting start at login: \(enable)", service: serviceName)

        if #available(macOS 13.0, *) {
            // Use the new SMAppService API for macOS 13+
            if enable {
                do {
                    try SMAppService.mainApp.register()
                    logger.info(
                        "Successfully registered login item using SMAppService",
                        service: serviceName)
                } catch {
                    logger.error(
                        "Failed to register login item using SMAppService: \(error)",
                        service: serviceName)
                }
            } else {
                do {
                    try SMAppService.mainApp.unregister()
                    logger.info(
                        "Successfully unregistered login item using SMAppService",
                        service: serviceName)
                } catch {
                    logger.error(
                        "Failed to unregister login item using SMAppService: \(error)",
                        service: serviceName)
                }
            }
        } else {
            // Use the legacy SMLoginItemSetEnabled API for macOS 12.4
            setStartAtLoginLegacy(enable)
        }
    }

    static func isStartingAtLogin() -> Bool {
        if #available(macOS 13.0, *) {
            let status = SMAppService.mainApp.status == .enabled
            logger.debug("Login item status (SMAppService): \(status)", service: serviceName)
            return status
        } else {
            return isStartingAtLoginLegacy()
        }
    }

    // MARK: - Legacy Implementation for macOS 12.4

    private static func setStartAtLoginLegacy(_ enable: Bool) {
        logger.info("Using legacy login item registration for macOS 12.4", service: serviceName)

        // Get the current app's bundle URL
        let appURL = Bundle.main.bundleURL
        logger.debug("Using app URL: \(appURL.path)", service: serviceName)

        // Use LSSharedFileList for macOS 12.4 compatibility
        // Note: These APIs are deprecated but still functional on macOS 12.4
        guard
            let loginItems = LSSharedFileListCreate(
                nil, kLSSharedFileListSessionLoginItems.takeUnretainedValue(), nil)?
                .takeRetainedValue()
        else {
            logger.error("Failed to create LSSharedFileList for login items", service: serviceName)
            return
        }

        if enable {
            // Add the app to login items
            let result = LSSharedFileListInsertItemURL(
                loginItems,
                kLSSharedFileListItemBeforeFirst.takeUnretainedValue(),
                nil,
                nil,
                appURL as CFURL,
                nil,
                nil
            )

            if result != nil {
                logger.info(
                    "Successfully added app to login items using LSSharedFileList",
                    service: serviceName)
                UserDefaults.standard.set(true, forKey: "StartAtLogin")
            } else {
                logger.error(
                    "Failed to add app to login items using LSSharedFileList", service: serviceName)
            }
        } else {
            // Remove the app from login items
            removeFromLoginItemsLegacy(loginItems: loginItems, appURL: appURL)
        }
    }

    /// Remove the app from login items using LSSharedFileList
    private static func removeFromLoginItemsLegacy(loginItems: LSSharedFileList, appURL: URL) {
        guard
            let loginItemsArray = LSSharedFileListCopySnapshot(loginItems, nil)?.takeRetainedValue()
                as? [LSSharedFileListItem]
        else {
            logger.error("Failed to get login items snapshot for removal", service: serviceName)
            return
        }

        var removed = false
        for item in loginItemsArray {
            if let itemURL = LSSharedFileListItemCopyResolvedURL(item, 0, nil)?.takeRetainedValue()
                as URL?
            {
                if itemURL.path == appURL.path {
                    let result = LSSharedFileListItemRemove(loginItems, item)
                    if result == noErr {
                        logger.info(
                            "Successfully removed app from login items using LSSharedFileList",
                            service: serviceName)
                        removed = true
                    } else {
                        logger.error(
                            "Failed to remove app from login items: OSStatus \(result)",
                            service: serviceName)
                    }
                    break
                }
            }
        }

        if removed {
            UserDefaults.standard.set(false, forKey: "StartAtLogin")
        } else {
            logger.warning(
                "App was not found in login items during removal attempt", service: serviceName)
            // Still update UserDefaults to reflect the intended state
            UserDefaults.standard.set(false, forKey: "StartAtLogin")
        }
    }

    private static func isStartingAtLoginLegacy() -> Bool {
        // For macOS 12.4, since SMLoginItemSetEnabled doesn't provide a direct way to check status,
        // we'll rely on UserDefaults to track the state. This is synchronized with the actual
        // login item registration in setStartAtLoginLegacy()
        let status = UserDefaults.standard.bool(forKey: "StartAtLogin")
        logger.debug("Login item status (UserDefaults): \(status)", service: serviceName)
        return status
    }
}
