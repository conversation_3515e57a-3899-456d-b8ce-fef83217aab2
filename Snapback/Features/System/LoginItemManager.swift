import Foundation
import ServiceManagement

class LoginItemManager {
    /// Logger for login item operations
    private static let logger = LoggingService.shared
    private static let serviceName = "LoginItemManager"

    static func setStartAtLogin(_ enable: Bool) {
        logger.info("Setting start at login: \(enable)", service: serviceName)

        if #available(macOS 13.0, *) {
            // Use the new SMAppService API for macOS 13+
            if enable {
                do {
                    try SMAppService.mainApp.register()
                    logger.info(
                        "Successfully registered login item using SMAppService",
                        service: serviceName)
                } catch {
                    logger.error(
                        "Failed to register login item using SMAppService: \(error)",
                        service: serviceName)
                }
            } else {
                do {
                    try SMAppService.mainApp.unregister()
                    logger.info(
                        "Successfully unregistered login item using SMAppService",
                        service: serviceName)
                } catch {
                    logger.error(
                        "Failed to unregister login item using SMAppService: \(error)",
                        service: serviceName)
                }
            }
        } else {
            // Use the legacy SMLoginItemSetEnabled API for macOS 12.4
            setStartAtLoginLegacy(enable)
        }
    }

    static func isStartingAtLogin() -> Bool {
        if #available(macOS 13.0, *) {
            let status = SMAppService.mainApp.status == .enabled
            logger.debug("Login item status (SMAppService): \(status)", service: serviceName)
            return status
        } else {
            return isStartingAtLoginLegacy()
        }
    }

    // MARK: - Legacy Implementation for macOS 12.4

    private static func setStartAtLoginLegacy(_ enable: Bool) {
        logger.info("Using legacy login item registration for macOS 12.4", service: serviceName)

        // Get the current app's bundle identifier
        guard let bundleIdentifier = Bundle.main.bundleIdentifier else {
            logger.error(
                "Failed to get bundle identifier for login item registration", service: serviceName)
            return
        }

        logger.debug("Using bundle identifier: \(bundleIdentifier)", service: serviceName)

        // Use SMLoginItemSetEnabled for macOS 12.4 compatibility
        let success = SMLoginItemSetEnabled(bundleIdentifier as CFString, enable)

        if success {
            logger.info(
                "Successfully \(enable ? "enabled" : "disabled") login item using SMLoginItemSetEnabled",
                service: serviceName)
            // Also save to UserDefaults for consistency checking
            UserDefaults.standard.set(enable, forKey: "StartAtLogin")
        } else {
            logger.error(
                "Failed to \(enable ? "enable" : "disable") login item using SMLoginItemSetEnabled",
                service: serviceName)
        }
    }

    private static func isStartingAtLoginLegacy() -> Bool {
        // For macOS 12.4, since SMLoginItemSetEnabled doesn't provide a direct way to check status,
        // we'll rely on UserDefaults to track the state. This is synchronized with the actual
        // login item registration in setStartAtLoginLegacy()
        let status = UserDefaults.standard.bool(forKey: "StartAtLogin")
        logger.debug("Login item status (UserDefaults): \(status)", service: serviceName)
        return status
    }
}
