import AppKit
import Carbon.HIToolbox
import Combine
import Foundation
import KeyboardShortcuts
import MA<PERSON>hortcut

// We're using the existing refreshStatusMenu notification name from ShortcutService

/// ShortcutService that uses the ShortcutManager wrapper
class ShortcutService {
    // Logger
    private let logger = LoggingService.shared
    private let serviceName = "ShortcutService"

    private let appDelegate: AppDelegate
    private let snappingService: WindowSnappingService
    private let workspaceService: WorkspaceService

    // The shortcut bridge
    private let shortcutBridge = KeyboardShortcutsBridge.shared

    // Keep track of registered workspace shortcuts
    private var registeredWorkspaceShortcuts: Set<UUID> = []

    // Notification observer
    private var workspacesObserver: AnyCancellable?

    init(
        appDelegate: AppDelegate, snappingService: WindowSnappingService,
        workspaceService: WorkspaceService
    ) {
        self.appDelegate = appDelegate
        self.snappingService = snappingService
        self.workspaceService = workspaceService

        // Observe workspace changes
        workspacesObserver = NotificationCenter.default.publisher(for: .workspacesDidChange)
            .sink { [weak self] _ in
                self?.updateShortcuts()
            }

        // Listen for immediate workspace shortcut registration requests
        NotificationCenter.default.addObserver(
            forName: Notification.Name("RegisterWorkspaceShortcutImmediately"),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let userInfo = notification.userInfo,
                let workspace = userInfo["workspace"] as? Workspace
            {
                self?.registerWorkspaceShortcutImmediately(for: workspace)
            }
        }

        // Listen for license status changes to update shortcuts
        NotificationCenter.default.addObserver(
            forName: Notification.Name("LicenseStatusChanged"),
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.logger.info(
                "License status changed, updating shortcuts",
                service: self?.serviceName ?? "ShortcutService",
                category: .shortcuts
            )
            self?.updateShortcuts()
        }

        // Initialize the shortcut bridge
        initializeShortcutBridge()
    }

    // Initialize the shortcut bridge with default shortcuts
    private func initializeShortcutBridge() {
        // Register all predefined shortcuts with the bridge
        registerPredefinedShortcutsWithBridge()
    }

    // Register all predefined shortcuts with the bridge
    private func registerPredefinedShortcutsWithBridge() {
        // Left Half
        shortcutBridge.registerName(
            .leftHalf,
            stringName: "leftHalf",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_LeftArrow),
                modifiers: [.control, .option]
            )
        )

        // Right Half
        shortcutBridge.registerName(
            .rightHalf,
            stringName: "rightHalf",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_RightArrow),
                modifiers: [.control, .option]
            )
        )

        // Top Half
        shortcutBridge.registerName(
            .topHalf,
            stringName: "topHalf",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_UpArrow),
                modifiers: [.control, .option]
            )
        )

        // Bottom Half
        shortcutBridge.registerName(
            .bottomHalf,
            stringName: "bottomHalf",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_DownArrow),
                modifiers: [.control, .option]
            )
        )

        // Top Left Quarter
        shortcutBridge.registerName(
            .topLeftQuarter,
            stringName: "topLeftQuarter",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_ANSI_U),
                modifiers: [.control, .option]
            )
        )

        // Top Right Quarter
        shortcutBridge.registerName(
            .topRightQuarter,
            stringName: "topRightQuarter",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_ANSI_I),
                modifiers: [.control, .option]
            )
        )

        // Bottom Left Quarter
        shortcutBridge.registerName(
            .bottomLeftQuarter,
            stringName: "bottomLeftQuarter",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_ANSI_J),
                modifiers: [.control, .option]
            )
        )

        // Bottom Right Quarter
        shortcutBridge.registerName(
            .bottomRightQuarter,
            stringName: "bottomRightQuarter",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_ANSI_K),
                modifiers: [.control, .option]
            )
        )

        // Left Third
        shortcutBridge.registerName(
            .leftThird,
            stringName: "leftThird",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_ANSI_D),
                modifiers: [.control, .option]
            )
        )

        // Center Third
        shortcutBridge.registerName(
            .centerThird,
            stringName: "centerThird",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_ANSI_F),
                modifiers: [.control, .option]
            )
        )

        // Right Third
        shortcutBridge.registerName(
            .rightThird,
            stringName: "rightThird",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_ANSI_G),
                modifiers: [.control, .option]
            )
        )

        // Left Two Thirds
        shortcutBridge.registerName(
            .leftTwoThirds,
            stringName: "leftTwoThirds",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_ANSI_E),
                modifiers: [.control, .option]
            )
        )

        // Center Two Thirds
        shortcutBridge.registerName(
            .centerTwoThirds,
            stringName: "centerTwoThirds",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_ANSI_R),
                modifiers: [.control, .option]
            )
        )

        // Right Two Thirds
        shortcutBridge.registerName(
            .rightTwoThirds,
            stringName: "rightTwoThirds",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_ANSI_T),
                modifiers: [.control, .option]
            )
        )

        // Fullscreen
        shortcutBridge.registerName(
            .fullscreen,
            stringName: "fullscreen",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_Return),
                modifiers: [.control, .option]
            )
        )

        // Save Workspace
        shortcutBridge.registerName(
            .saveWorkspace,
            stringName: "saveWorkspace",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_ANSI_S),
                modifiers: [.control, .option]
            )
        )

        // Quit App
        shortcutBridge.registerName(
            .quitApp,
            stringName: "quitApp",
            defaultShortcut: Shortcut(
                keyCode: Int(kVK_ANSI_Q),
                modifiers: [.control, .option]
            )
        )
    }

    // Register all keyboard shortcuts
    func registerKeyboardShortcuts() {
        logger.debug(
            "Registering keyboard shortcuts...",
            service: serviceName,
            category: .shortcuts
        )

        // FREEMIUM MODEL: Keyboard shortcuts are available to all users
        // Only workspace creation is limited for free users
        let licenseManager = LicenseManager.shared
        let isLicenseSystemEnabled = licenseManager.isLicenseSystemEnabled

        logger.debug(
            "License system enabled: \(isLicenseSystemEnabled), status: \(licenseManager.licenseStatus)",
            service: serviceName,
            category: .shortcuts
        )

        // Note: Shortcuts are now available to all users in freemium model
        logger.info(
            "Registering shortcuts for all users (freemium model)",
            service: serviceName,
            category: .shortcuts
        )

        // Check if window management features are enabled
        let windowManagementEnabled = DefaultsManager.shared.windowManagementEnabled

        if windowManagementEnabled {
            // Register window management shortcuts
            registerWindowManagementShortcuts()
        } else {
            logger.debug(
                "Window management features disabled, skipping window management shortcuts",
                service: serviceName,
                category: .shortcuts
            )
        }

        // Register workspace shortcuts (Save Workspace)
        registerWorkspaceShortcuts()

        logger.debug(
            "All shortcuts registered.",
            service: serviceName,
            category: .shortcuts
        )
    }

    // Update shortcuts (called when workspaces change)
    public func updateShortcuts() {
        logger.debug(
            "Updating shortcuts...",
            service: serviceName,
            category: .shortcuts
        )

        // Unregister all shortcuts
        unregisterAllShortcuts()

        // Register all shortcuts again
        registerKeyboardShortcuts()
    }

    // Register a specific workspace shortcut immediately (for new workspaces)
    public func registerWorkspaceShortcutImmediately(for workspace: Workspace) {
        // Check if there's a temporary shortcut we should transfer first
        let tempShortcutName = KeyboardShortcuts.Name("temp_workspace_shortcut")
        let tempShortcut = KeyboardShortcuts.getShortcut(for: tempShortcutName)

        if let tempShortcut = tempShortcut, let key = tempShortcut.key {
            // Transfer the temporary shortcut to the workspace
            let shortcut = Shortcut(keyCode: key.rawValue, modifiers: tempShortcut.modifiers)
            registerWorkspaceShortcut(
                for: workspace.id!, workspace: workspace, shortcut: shortcut, useDelay: true)
        } else {
            // Use existing shortcut from workspace model
            registerWorkspaceShortcut(
                for: workspace.id!, workspace: workspace, shortcut: nil, useDelay: true)
        }
    }

    // Reset all shortcuts to defaults
    func resetAllShortcutsToDefaults() {
        logger.info(
            "Resetting all shortcuts to defaults...",
            service: serviceName,
            category: .shortcuts
        )

        // Reset all shortcuts using the bridge
        shortcutBridge.resetAllShortcuts()

        // Update shortcuts
        updateShortcuts()

        logger.info(
            "All shortcuts reset to defaults",
            service: serviceName,
            category: .shortcuts
        )
    }

    // Register window management shortcuts
    func registerWindowManagementShortcuts() {
        logger.debug(
            "Registering window management shortcuts...",
            service: serviceName,
            category: .shortcuts
        )

        // Left Half
        shortcutBridge.registerShortcutHandler(for: .leftHalf) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .leftHalf)
        }

        // Right Half
        shortcutBridge.registerShortcutHandler(for: .rightHalf) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .rightHalf)
        }

        // Top Half
        shortcutBridge.registerShortcutHandler(for: .topHalf) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .topHalf)
        }

        // Bottom Half
        shortcutBridge.registerShortcutHandler(for: .bottomHalf) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .bottomHalf)
        }

        // Top Left Quarter
        shortcutBridge.registerShortcutHandler(for: .topLeftQuarter) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .topLeftQuarter)
        }

        // Top Right Quarter
        shortcutBridge.registerShortcutHandler(for: .topRightQuarter) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .topRightQuarter)
        }

        // Bottom Left Quarter
        shortcutBridge.registerShortcutHandler(for: .bottomLeftQuarter) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .bottomLeftQuarter)
        }

        // Bottom Right Quarter
        shortcutBridge.registerShortcutHandler(for: .bottomRightQuarter) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .bottomRightQuarter)
        }

        // Left Third
        shortcutBridge.registerShortcutHandler(for: .leftThird) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .leftThird)
        }

        // Center Third
        shortcutBridge.registerShortcutHandler(for: .centerThird) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .centerThird)
        }

        // Right Third
        shortcutBridge.registerShortcutHandler(for: .rightThird) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .rightThird)
        }

        // Left Two Thirds
        shortcutBridge.registerShortcutHandler(for: .leftTwoThirds) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .leftTwoThirds)
        }

        // Center Two Thirds
        shortcutBridge.registerShortcutHandler(for: .centerTwoThirds) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .centerTwoThirds)
        }

        // Right Two Thirds
        shortcutBridge.registerShortcutHandler(for: .rightTwoThirds) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .rightTwoThirds)
        }

        // Fullscreen
        shortcutBridge.registerShortcutHandler(for: .fullscreen) { [weak self] in
            self?.snappingService.snapFrontmostWindow(to: .fullscreen)
        }

        logger.debug(
            "Window management shortcuts registered",
            service: serviceName,
            category: .shortcuts
        )
    }

    // Unregister window management shortcuts
    func unregisterWindowManagementShortcuts() {
        logger.debug(
            "Unregistering window management shortcuts...",
            service: serviceName,
            category: .shortcuts
        )

        // Since KeyboardShortcuts doesn't have individual removeHandler method,
        // we'll use the approach of removing all handlers and re-registering only workspace shortcuts
        KeyboardShortcuts.removeAllHandlers()

        // Re-register workspace shortcuts only
        registerWorkspaceShortcuts()

        logger.debug(
            "Window management shortcuts unregistered",
            service: serviceName,
            category: .shortcuts
        )
    }

    // Register a shortcut for a specific workspace
    private func registerWorkspaceShortcut(
        for id: UUID, workspace: Workspace, shortcut: Shortcut? = nil, useDelay: Bool = false
    ) {
        logger.debug(
            "Registering shortcut for workspace '\(workspace.name)' with ID \(id.uuidString)",
            service: serviceName
        )

        let shortcutName = KeyboardShortcuts.Name.workspaceShortcut(for: id)

        // Register the name with the bridge
        shortcutBridge.registerName(shortcutName, stringName: "workspace_\(id.uuidString)")

        // Determine which shortcut to use
        var shortcutToSet: Shortcut?

        if let providedShortcut = shortcut {
            // Use the provided shortcut (e.g., from temporary shortcut)
            shortcutToSet = providedShortcut
        } else if let keyCode = workspace.shortcutKeyCode,
            let modifiers = workspace.shortcutModifiers
        {
            // Use shortcut from workspace model
            shortcutToSet = Shortcut(
                keyCode: Int(keyCode),
                modifiers: NSEvent.ModifierFlags(rawValue: modifiers)
            )
        } else {
            // Check if there's already a shortcut set in KeyboardShortcuts
            let existingShortcut = KeyboardShortcuts.getShortcut(for: shortcutName)
            if existingShortcut != nil {
                logger.debug(
                    "Using existing shortcut from KeyboardShortcuts",
                    service: serviceName,
                    category: .shortcuts
                )
            } else {
                logger.debug(
                    "No shortcut found for workspace '\(workspace.name)'",
                    service: serviceName,
                    category: .shortcuts
                )
                return
            }
        }

        // Set the shortcut if we have one
        if let shortcut = shortcutToSet {
            shortcutBridge.setShortcut(shortcut, for: shortcutName)
        }

        // Register the shortcut handler with optional delay
        let registerHandler = { [weak self] in
            self?.shortcutBridge.registerShortcutHandler(for: shortcutName) { [weak self] in
                self?.logger.info(
                    "Workspace shortcut triggered for '\(workspace.name)'",
                    service: self?.serviceName ?? "ShortcutService",
                    category: .shortcuts
                )
                self?.workspaceService.triggerRestoreWorkspace(workspace: workspace)
            }
        }

        if useDelay {
            // Add delay for immediate registration to fix timing issue
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                registerHandler()
            }
        } else {
            // Register immediately for existing shortcuts
            registerHandler()
        }

        // Add to registered shortcuts
        registeredWorkspaceShortcuts.insert(id)

        logger.debug(
            "Successfully registered shortcut handler for workspace '\(workspace.name)'",
            service: serviceName,
            category: .shortcuts
        )
    }

    // Register workspace shortcuts
    private func registerWorkspaceShortcuts() {
        logger.debug("Registering workspace shortcuts...", service: serviceName)

        // Always register Save Workspace shortcut
        shortcutBridge.registerShortcutHandler(for: .saveWorkspace) { [weak self] in
            self?.logger.info(
                "Save Workspace shortcut triggered",
                service: self?.serviceName ?? "ShortcutService",
                category: .shortcuts
            )
            self?.appDelegate.saveCurrentWorkspace()
        }

        // Always register Quit App shortcut
        shortcutBridge.registerShortcutHandler(for: .quitApp) { [weak self] in
            self?.logger.info(
                "Quit App shortcut triggered",
                service: self?.serviceName ?? "ShortcutService",
                category: .shortcuts
            )
            NSApp.terminate(nil)
        }

        // Register all workspace shortcuts
        for workspace in workspaceService.workspaces {
            if let id = workspace.id {
                registerWorkspaceShortcut(for: id, workspace: workspace)
            }
        }

        logger.debug("Workspace shortcuts registration complete", service: serviceName)
    }

    // Unregister all shortcuts
    private func unregisterAllShortcuts() {
        // Unregister all shortcuts by removing all handlers
        KeyboardShortcuts.removeAllHandlers()

        // Clear the list of registered workspace shortcuts
        registeredWorkspaceShortcuts.removeAll()

        logger.debug("All shortcuts unregistered", service: serviceName)
    }

    // Migrate existing shortcuts to the new system
    func migrateExistingShortcuts() {
        logger.debug("Migrating existing shortcuts...", service: serviceName)

        // Check if we need to migrate
        let needsMigration = !UserDefaults.standard.bool(
            forKey: "ShortcutManagerMigrationCompleted")

        if needsMigration {
            logger.debug("First-time migration needed", service: serviceName)

            // Reset all shortcuts to their defaults
            shortcutBridge.resetAllShortcuts()

            // Mark migration as completed
            UserDefaults.standard.set(true, forKey: "ShortcutManagerMigrationCompleted")
            logger.debug("Migration marked as completed", service: serviceName)
        } else {
            logger.debug(
                "Migration already completed, using existing values",
                service: serviceName
            )
        }

        // Ensure Save Workspace shortcut has its default value if not set
        ensureSaveWorkspaceShortcutDefault()

        // One-time fix for Save Workspace shortcut inconsistency
        performSaveWorkspaceShortcutFix()

        logger.debug("Migration complete.", service: serviceName)
    }

    // Ensure Save Workspace shortcut has its default value
    private func ensureSaveWorkspaceShortcutDefault() {
        let existingShortcut = KeyboardShortcuts.getShortcut(for: .saveWorkspace)

        // Check if the existing shortcut has the wrong modifiers (Control + Shift instead of Control + Option)
        let wrongModifiers = NSEvent.ModifierFlags([.control, .shift])
        let correctModifiers = NSEvent.ModifierFlags([.control, .option])

        if let shortcut = existingShortcut {
            logger.debug(
                "Save Workspace shortcut exists with modifiers: \(shortcut.modifiers.rawValue)",
                service: serviceName,
                category: .shortcuts
            )

            // If it has the wrong modifiers, reset it to the correct default
            if shortcut.modifiers == wrongModifiers {
                logger.info(
                    "Save Workspace shortcut has wrong modifiers (Control+Shift), resetting to Control+Option",
                    service: serviceName,
                    category: .shortcuts
                )

                // Set the correct default shortcut (Control + Option + S)
                let defaultShortcut = KeyboardShortcuts.Shortcut(.s, modifiers: correctModifiers)
                KeyboardShortcuts.setShortcut(defaultShortcut, for: .saveWorkspace)

                logger.info(
                    "Save Workspace shortcut reset to correct default: Control + Option + S",
                    service: serviceName,
                    category: .shortcuts
                )
            } else {
                logger.debug(
                    "Save Workspace shortcut has correct modifiers",
                    service: serviceName,
                    category: .shortcuts
                )
            }
        } else {
            logger.debug(
                "Save Workspace shortcut not set, setting to default",
                service: serviceName,
                category: .shortcuts
            )

            // Set the default shortcut (Control + Option + S)
            let defaultShortcut = KeyboardShortcuts.Shortcut(.s, modifiers: correctModifiers)
            KeyboardShortcuts.setShortcut(defaultShortcut, for: .saveWorkspace)

            logger.debug(
                "Save Workspace shortcut set to default: Control + Option + S",
                service: serviceName,
                category: .shortcuts
            )
        }
    }

    // One-time fix for Save Workspace shortcut inconsistency
    private func performSaveWorkspaceShortcutFix() {
        let fixKey = "SaveWorkspaceShortcutFixApplied"

        // Check if we've already applied this fix
        if UserDefaults.standard.bool(forKey: fixKey) {
            logger.debug(
                "Save Workspace shortcut fix already applied",
                service: serviceName,
                category: .shortcuts
            )
            return
        }

        logger.info(
            "Applying one-time Save Workspace shortcut fix",
            service: serviceName,
            category: .shortcuts
        )

        // Force reset the Save Workspace shortcut to the correct default
        let correctShortcut = KeyboardShortcuts.Shortcut(.s, modifiers: [.control, .option])
        KeyboardShortcuts.setShortcut(correctShortcut, for: .saveWorkspace)

        // Mark the fix as applied
        UserDefaults.standard.set(true, forKey: fixKey)

        logger.info(
            "Save Workspace shortcut fix applied - reset to Control + Option + S",
            service: serviceName,
            category: .shortcuts
        )
    }

}
